import { Repository } from 'typeorm';
import { User } from './user.entity';
import { JwtService } from '@nestjs/jwt';
import { RefreshTokenService } from './refresh-token.service';
export declare class AuthService {
    private userRepo;
    private jwtService;
    private refreshTokenService;
    private chatGateway;
    constructor(userRepo: Repository<User>, jwtService: JwtService, refreshTokenService: RefreshTokenService, chatGateway: any);
    register(username: string, password: string): Promise<{
        user: User;
        access_token: string;
        refresh_token: string;
    }>;
    login(username: string, password: string, deviceInfo?: {
        deviceId?: string;
        userAgent?: string;
        ipAddress?: string;
    }): Promise<{
        access_token: string;
        refresh_token: string;
    }>;
    refreshAccessToken(refreshTokenString: string): Promise<{
        access_token: string;
        refresh_token: string;
    }>;
    logout(refreshTokenString: string): Promise<void>;
    logoutAll(userId: string): Promise<void>;
}
