"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefreshTokenService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const refresh_token_entity_1 = require("./refresh-token.entity");
const crypto_1 = require("crypto");
let RefreshTokenService = class RefreshTokenService {
    refreshTokenRepo;
    configService;
    constructor(refreshTokenRepo, configService) {
        this.refreshTokenRepo = refreshTokenRepo;
        this.configService = configService;
    }
    async generateRefreshToken(userId, deviceInfo) {
        const token = (0, crypto_1.randomBytes)(64).toString('hex');
        const expirationDays = this.parseExpirationTime(this.configService.get('JWT_REFRESH_EXPIRATION', '7d'));
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + expirationDays);
        const refreshToken = this.refreshTokenRepo.create({
            token,
            userId,
            expiresAt,
            isRevoked: false,
            deviceId: deviceInfo?.deviceId,
            userAgent: deviceInfo?.userAgent,
            ipAddress: deviceInfo?.ipAddress,
        });
        return this.refreshTokenRepo.save(refreshToken);
    }
    async validateRefreshToken(token) {
        const refreshToken = await this.refreshTokenRepo.findOne({
            where: { token, isRevoked: false },
            relations: ['user'],
        });
        if (!refreshToken) {
            return null;
        }
        if (refreshToken.expiresAt < new Date()) {
            await this.revokeRefreshToken(token);
            return null;
        }
        return refreshToken;
    }
    async revokeRefreshToken(token) {
        await this.refreshTokenRepo.update({ token }, { isRevoked: true });
    }
    async revokeAllUserTokens(userId) {
        await this.refreshTokenRepo.update({ userId, isRevoked: false }, { isRevoked: true });
    }
    async hasValidTokens(userId) {
        const validTokenCount = await this.refreshTokenRepo.count({
            where: {
                userId,
                isRevoked: false,
                expiresAt: (0, typeorm_2.MoreThan)(new Date())
            }
        });
        return validTokenCount > 0;
    }
    async rotateRefreshToken(oldToken, userId) {
        await this.revokeRefreshToken(oldToken);
        return this.generateRefreshToken(userId);
    }
    async cleanupExpiredTokens() {
        await this.refreshTokenRepo.delete({
            expiresAt: (0, typeorm_2.LessThan)(new Date()),
        });
    }
    parseExpirationTime(expiration) {
        const unit = expiration.slice(-1);
        const value = parseInt(expiration.slice(0, -1));
        switch (unit) {
            case 'd':
                return value;
            case 'h':
                return value / 24;
            case 'm':
                return value / (24 * 60);
            default:
                return 7;
        }
    }
};
exports.RefreshTokenService = RefreshTokenService;
exports.RefreshTokenService = RefreshTokenService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(refresh_token_entity_1.RefreshToken)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        config_1.ConfigService])
], RefreshTokenService);
//# sourceMappingURL=refresh-token.service.js.map