"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const auth_controller_1 = require("./auth.controller");
const typeorm_1 = require("@nestjs/typeorm");
const user_entity_1 = require("./user.entity");
const refresh_token_entity_1 = require("./refresh-token.entity");
const jwt_1 = require("@nestjs/jwt");
const jwt_strategy_1 = require("./jwt.strategy");
const users_controller_1 = require("./users.controller");
const users_service_1 = require("./users.service");
const refresh_token_service_1 = require("./refresh-token.service");
const config_1 = require("@nestjs/config");
const chat_module_1 = require("../chat/chat.module");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User, refresh_token_entity_1.RefreshToken]),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                useFactory: (configService) => ({
                    secret: configService.get('JWT_SECRET', 'your_jwt_secret'),
                    signOptions: { expiresIn: configService.get('JWT_ACCESS_EXPIRATION', '15m') },
                }),
            }),
            (0, common_1.forwardRef)(() => chat_module_1.ChatModule),
        ],
        controllers: [auth_controller_1.AuthController, users_controller_1.UsersController],
        providers: [auth_service_1.AuthService, jwt_strategy_1.JwtStrategy, users_service_1.UsersService, refresh_token_service_1.RefreshTokenService],
        exports: [auth_service_1.AuthService, users_service_1.UsersService, refresh_token_service_1.RefreshTokenService],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map